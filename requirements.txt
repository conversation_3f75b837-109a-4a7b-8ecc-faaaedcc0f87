# Build dependencies (must be first)
Cython>=0.29.33

# Core dependencies
streamlit>=1.20.0
pandas>=1.3.0
# Pin NumPy to a version before 2.0 to avoid compatibility issues with Prophet
numpy<2.0.0

# Visualization libraries
plotly>=5.3.0  # Includes plotly.subplots for side-by-side charts
matplotlib>=3.5.0
seaborn>=0.11.0

# Data handling
openpyxl>=3.0.0

# Statistical and forecasting libraries
statsmodels>=0.13.0
scikit-learn>=1.0.0

# Streamlit extensions
streamlit-extras>=0.2.0
streamlit-option-menu>=0.3.0

# Prophet and its dependencies
# Using a specific version of <PERSON> that works with Python 3.12
prophet==1.1.4
holidays>=0.25
# Add specific dependencies for <PERSON> to ensure compatibility
cmdstanpy>=1.0.4
pystan>=3.5.0

# PDF generation
fpdf2>=2.7.0

# Note: We need to specify these dependencies to ensure proper installation on Streamlit Cloud
