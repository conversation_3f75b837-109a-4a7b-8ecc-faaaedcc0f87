# Manual Redeployment Instructions for Streamlit Cloud

1. Go to [Streamlit Cloud](https://share.streamlit.io/)
2. Sign in with the same account you used to deploy the app
3. Find your app in the dashboard
4. Click on the three dots (⋮) next to your app
5. Select "Manage app"
6. In the app settings page, find the "Advanced settings" section
7. Click on "Reboot app"
8. Wait for the app to reboot (this will trigger a fresh deployment)

This will force Streamlit Cloud to pull the latest changes from your GitHub repository and rebuild the app.
